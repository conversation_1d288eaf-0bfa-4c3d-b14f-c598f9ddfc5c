#!/usr/bin/env python3
"""
调试 time_diff 注解函数
"""

import os
import sys
import json
import django
from typing import Dict, Any

# 配置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "flights_qa_system.settings")
django.setup()

from flights_workflow.tools.query_parser import parse_flight_query
from flights_workflow.tools.comprehensive_query_tool import comprehensive_flight_query


def test_time_diff_annotation():
    """测试 time_diff 注解函数"""
    print("测试 time_diff 注解函数...")
    
    # 延误航班查询
    delay_query = {
        "model": "aviation_flight",
        "conditions": {
            "AND": [
                {"field": "departure_actual_time", "operator": "isnull", "value": False},
                {"field": "departure_scheduled_time", "operator": "isnull", "value": False}
            ]
        },
        "annotations": {
            "delay_minutes": {
                "function": "time_diff",
                "field1": "departure_actual_time",
                "field2": "departure_scheduled_time",
                "unit": "minutes"
            }
        },
        "conditions_after_annotations": {
            "AND": [
                {"field": "delay_minutes", "operator": "gt", "value": 15}
            ]
        },
        "ordering": ["-delay_minutes"],
        "limit": 10
    }
    
    print(f"查询Schema: {json.dumps(delay_query, ensure_ascii=False, indent=2)}")
    
    try:
        # 尝试解析查询
        queryset = parse_flight_query(delay_query)
        print(f"✅ 查询解析成功")
        print(f"QuerySet类型: {type(queryset)}")
        
        # 尝试获取SQL
        if hasattr(queryset, 'query'):
            print(f"生成的SQL: {str(queryset.query)}")
        
        # 尝试执行查询
        results = list(queryset.values())
        print(f"✅ 查询执行成功，结果数量: {len(results)}")
        
        if results:
            print(f"示例结果: {json.dumps(results[0], ensure_ascii=False, indent=2, default=str)}")
        
    except Exception as e:
        print(f"❌ 查询失败: {str(e)}")
        import traceback
        traceback.print_exc()


def test_comprehensive_time_diff():
    """测试通过 comprehensive_flight_query 工具的 time_diff 功能"""
    print("\n测试通过 comprehensive_flight_query 工具的 time_diff 功能...")
    
    delay_query = {
        "model": "aviation_flight",
        "conditions": {
            "AND": [
                {"field": "departure_actual_time", "operator": "isnull", "value": False},
                {"field": "departure_scheduled_time", "operator": "isnull", "value": False}
            ]
        },
        "annotations": {
            "delay_minutes": {
                "function": "time_diff",
                "field1": "departure_actual_time",
                "field2": "departure_scheduled_time",
                "unit": "minutes"
            }
        },
        "conditions_after_annotations": {
            "AND": [
                {"field": "delay_minutes", "operator": "gt", "value": 15}
            ]
        },
        "ordering": ["-delay_minutes"],
        "limit": 5
    }
    
    query_schema = json.dumps(delay_query, ensure_ascii=False)
    user_question = "查询延误的航班"
    
    try:
        # 调用 comprehensive_flight_query 工具
        result = comprehensive_flight_query.invoke({
            "query_schema": query_schema, 
            "user_question": user_question
        })
        
        # 解析结果
        result_dict = json.loads(result)
        
        if result_dict.get('success'):
            print(f"✅ 综合查询成功")
            print(f"查询类型: {result_dict.get('query_type', 'unknown')}")
            print(f"结果数量: {result_dict.get('count', 0)}")
            
            # 显示部分结果
            if result_dict.get('result'):
                result_data = result_dict['result']
                if isinstance(result_data, list) and len(result_data) > 0:
                    print(f"示例结果: {json.dumps(result_data[0], ensure_ascii=False, indent=2, default=str)}")
        else:
            print(f"❌ 综合查询失败")
            print(f"错误阶段: {result_dict.get('stage', 'unknown')}")
            print(f"错误信息: {result_dict.get('error', 'unknown')}")
            if 'suggestions' in result_dict:
                print(f"建议: {result_dict['suggestions']}")
        
    except Exception as e:
        print(f"❌ 综合查询异常: {str(e)}")
        import traceback
        traceback.print_exc()


def test_simple_time_diff():
    """测试简单的 time_diff 注解"""
    print("\n测试简单的 time_diff 注解...")
    
    simple_query = {
        "model": "aviation_flight",
        "conditions": {
            "AND": [
                {"field": "departure_actual_time", "operator": "isnull", "value": False},
                {"field": "departure_scheduled_time", "operator": "isnull", "value": False}
            ]
        },
        "annotations": {
            "delay_minutes": {
                "function": "time_diff",
                "field1": "departure_actual_time",
                "field2": "departure_scheduled_time",
                "unit": "minutes"
            }
        },
        "limit": 5
    }
    
    try:
        queryset = parse_flight_query(simple_query)
        results = list(queryset.values())
        print(f"✅ 简单 time_diff 查询成功，结果数量: {len(results)}")
        
        if results:
            print(f"示例结果: {json.dumps(results[0], ensure_ascii=False, indent=2, default=str)}")
            
    except Exception as e:
        print(f"❌ 简单 time_diff 查询失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_simple_time_diff()
    test_time_diff_annotation()
    test_comprehensive_time_diff()
