#!/usr/bin/env python3
"""
调试机场查询问题
"""

import os
import sys
import json
import django
from typing import Dict, Any

# 配置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "flights_qa_system.settings")
django.setup()

from flights_workflow.tools.query_parser import parse_flight_query


def test_airport_query():
    """测试机场查询"""
    print("测试机场查询问题...")
    
    # 问题查询
    airport_query = {
        "model": "aviation_airports",
        "conditions": {
            "AND": [
                {"field": "country", "operator": "exact", "value": "China"},
                {"field": "name_en", "operator": "icontains", "value": "international"}
            ]
        },
        "ordering": ["name"],
        "limit": 10
    }
    
    print(f"查询Schema: {json.dumps(airport_query, ensure_ascii=False, indent=2)}")
    
    try:
        # 尝试解析查询
        queryset = parse_flight_query(airport_query)
        print(f"✅ 查询解析成功")
        print(f"QuerySet类型: {type(queryset)}")
        
        # 尝试获取SQL
        if hasattr(queryset, 'query'):
            print(f"生成的SQL: {str(queryset.query)}")
        
        # 尝试执行查询
        results = list(queryset.values())
        print(f"✅ 查询执行成功，结果数量: {len(results)}")
        
        if results:
            print(f"示例结果: {json.dumps(results[0], ensure_ascii=False, indent=2, default=str)}")
        
    except Exception as e:
        print(f"❌ 查询失败: {str(e)}")
        import traceback
        traceback.print_exc()


def test_simple_airport_query():
    """测试简单的机场查询"""
    print("\n测试简单机场查询...")
    
    simple_query = {
        "model": "aviation_airports",
        "conditions": {},
        "limit": 5
    }
    
    try:
        queryset = parse_flight_query(simple_query)
        results = list(queryset.values())
        print(f"✅ 简单查询成功，结果数量: {len(results)}")
        
        if results:
            print(f"示例结果: {json.dumps(results[0], ensure_ascii=False, indent=2, default=str)}")
            
    except Exception as e:
        print(f"❌ 简单查询失败: {str(e)}")
        import traceback
        traceback.print_exc()


def test_model_access():
    """测试模型访问"""
    print("\n测试模型访问...")
    
    try:
        from django.apps import apps
        model_class = apps.get_model('flights', 'aviation_airports')
        print(f"✅ 模型获取成功: {model_class}")
        
        # 测试基本查询
        queryset = model_class.objects.all()[:5]
        results = list(queryset.values())
        print(f"✅ 基本查询成功，结果数量: {len(results)}")
        
        if results:
            print(f"字段列表: {list(results[0].keys())}")
            
    except Exception as e:
        print(f"❌ 模型访问失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_model_access()
    test_simple_airport_query()
    test_airport_query()
